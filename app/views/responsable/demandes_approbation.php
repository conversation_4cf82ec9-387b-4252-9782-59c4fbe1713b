<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Demandes à approuver - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <script src="/assets/js/modal.js"></script>
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_responsable.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Demandes à approuver</h1>
                    <p class="text-gray-600">Validation des demandes de congés des membres de votre équipe</p>
                </div>
                <a href="/responsable/historique_demandes" class="text-indigo-600 hover:text-indigo-800 flex items-center">
                    <i class="fas fa-history mr-1"></i> Voir l'historique
                </a>
            </div>
        </header>

        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="bg-<?= $_SESSION['flash_message']['type'] === 'success' ? 'green' : 'red' ?>-100 border-l-4 border-<?= $_SESSION['flash_message']['type'] === 'success' ? 'green' : 'red' ?>-500 text-<?= $_SESSION['flash_message']['type'] === 'success' ? 'green' : 'red' ?>-700 p-4 mb-4 rounded">
                <p><?= $_SESSION['flash_message']['message'] ?></p>
            </div>
            <?php unset($_SESSION['flash_message']); ?>
        <?php endif; ?>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="filter-card bg-white rounded-lg shadow p-4 border-l-4 border-indigo-500 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                 data-filter="all"
                 tabindex="0"
                 role="button"
                 aria-label="Filtrer toutes les demandes"
                 onclick="filterRequests('all')"
                 onkeydown="handleCardKeydown(event, 'all')">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-indigo-100 text-indigo-500 mr-4">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Demandes Total</p>
                        <p class="text-xl font-bold"><?= count($demandes) ?></p>
                    </div>
                </div>
            </div>
            <div class="filter-card bg-white rounded-lg shadow p-4 border-l-4 border-blue-500 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                 data-filter="payé"
                 tabindex="0"
                 role="button"
                 aria-label="Filtrer les congés payés"
                 onclick="filterRequests('payé')"
                 onkeydown="handleCardKeydown(event, 'payé')">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                        <i class="fas fa-umbrella-beach"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Congés Payés</p>
                        <p class="text-xl font-bold"><?= isset($requestsByType['paye']) ? $requestsByType['paye'] : 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="filter-card bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
                 data-filter="exceptionnel"
                 tabindex="0"
                 role="button"
                 aria-label="Filtrer les congés exceptionnels"
                 onclick="filterRequests('exceptionnel')"
                 onkeydown="handleCardKeydown(event, 'exceptionnel')">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Exceptionnels</p>
                        <p class="text-xl font-bold"><?= isset($requestsByType['exceptionnel']) ? $requestsByType['exceptionnel'] : 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="filter-card bg-white rounded-lg shadow p-4 border-l-4 border-red-500 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                 data-filter="maladie"
                 tabindex="0"
                 role="button"
                 aria-label="Filtrer les congés maladie"
                 onclick="filterRequests('maladie')"
                 onkeydown="handleCardKeydown(event, 'maladie')">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-500 mr-4">
                        <i class="fas fa-medkit"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Maladie</p>
                        <p class="text-xl font-bold"><?= isset($requestsByType['maladie']) ? $requestsByType['maladie'] : 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="filter-card bg-white rounded-lg shadow p-4 border-l-4 border-green-500 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                 data-filter="familial"
                 tabindex="0"
                 role="button"
                 aria-label="Filtrer les congés familiaux"
                 onclick="filterRequests('familial')"
                 onkeydown="handleCardKeydown(event, 'familial')">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-500 mr-4">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Familial</p>
                        <p class="text-xl font-bold"><?= isset($requestsByType['familial']) ? $requestsByType['familial'] : 0 ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="card mb-6">
            <form action="/responsable/demandes_approbation" method="GET" class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <div class="flex-1">
                    <label for="employee" class="block text-sm font-medium text-gray-700 mb-1">Employé</label>
                    <select id="employee" name="employee" class="block w-full p-2 border border-gray-300 rounded-md">
                        <option value="all" <?= !isset($employeeFilter) || $employeeFilter === 'all' ? 'selected' : '' ?>>Tous les employés</option>
                        <?php if (isset($teamMembers)) foreach ($teamMembers as $member): ?>
                            <option value="<?= $member['id'] ?>" <?= isset($employeeFilter) && $employeeFilter == $member['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($member['nom'] . ' ' . $member['prenom']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="flex-1">
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type de congé</label>
                    <select id="type" name="type" class="block w-full p-2 border border-gray-300 rounded-md">
                        <option value="all" <?= !isset($typeFilter) || $typeFilter === 'all' ? 'selected' : '' ?>>Tous les types</option>
                        <option value="payé" <?= isset($typeFilter) && $typeFilter === 'payé' ? 'selected' : '' ?>>Congé Payé</option>
                        <option value="sans solde" <?= isset($typeFilter) && $typeFilter === 'sans solde' ? 'selected' : '' ?>>Sans Solde</option>
                        <option value="exceptionnel" <?= isset($typeFilter) && $typeFilter === 'exceptionnel' ? 'selected' : '' ?>>Exceptionnel</option>
                        <option value="maladie" <?= isset($typeFilter) && $typeFilter === 'maladie' ? 'selected' : '' ?>>Maladie</option>
                        <option value="familial" <?= isset($typeFilter) && $typeFilter === 'familial' ? 'selected' : '' ?>>Familial</option>
                    </select>
                </div>
                <div class="flex-1">
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date de soumission</label>
                    <select id="date" name="date" class="block w-full p-2 border border-gray-300 rounded-md">
                        <option value="all" <?= !isset($dateFilter) || $dateFilter === 'all' ? 'selected' : '' ?>>Toutes les dates</option>
                        <option value="recent" <?= isset($dateFilter) && $dateFilter === 'recent' ? 'selected' : '' ?>>Dernières 48 heures</option>
                        <option value="week" <?= isset($dateFilter) && $dateFilter === 'week' ? 'selected' : '' ?>>Dernière semaine</option>
                        <option value="month" <?= isset($dateFilter) && $dateFilter === 'month' ? 'selected' : '' ?>>Dernier mois</option>
                    </select>
                </div>
                <div class="flex-none self-end">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md">
                        <i class="fas fa-filter mr-1"></i> Filtrer
                    </button>
                </div>
            </form>
        </div>

        <?php if (empty($demandes)): ?>
            <div class="card text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune demande en attente</h3>
                <p class="text-gray-500">Toutes les demandes de congés ont été traitées. Revenez plus tard pour vérifier si de nouvelles demandes ont été soumises.</p>
            </div>
        <?php else: ?>
            <div class="space-y-4" id="requests-container">
                <?php foreach ($demandes as $demande): ?>
                    <div class="card relative overflow-hidden request-item" data-type="<?= htmlspecialchars($demande['type']) ?>">
                        <div class="absolute top-0 bottom-0 left-0 w-1 bg-indigo-500"></div>
                        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                            <div class="mb-4 md:mb-0">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                                        <span class="text-indigo-800 font-medium"><?= strtoupper(substr($demande['prenom'], 0, 1) . substr($demande['nom'], 0, 1)) ?></span>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900"><?= htmlspecialchars($demande['prenom']) ?> <?= htmlspecialchars($demande['nom']) ?></h3>
                                        <p class="text-sm text-gray-500">Demande soumise le <?= date('d/m/Y à H:i', strtotime($demande['date_demande'])) ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button type="button"
                                        class="details-btn inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        data-employee="<?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-type="<?= htmlspecialchars($demande['type'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-date-debut="<?= htmlspecialchars(date('d/m/Y', strtotime($demande['date_debut'])), ENT_QUOTES, 'UTF-8') ?>"
                                        data-date-fin="<?= htmlspecialchars(date('d/m/Y', strtotime($demande['date_fin'])), ENT_QUOTES, 'UTF-8') ?>"
                                        data-duree="<?= htmlspecialchars($demande['nbJours'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-motif="<?= htmlspecialchars($demande['motif'] ?? 'Non spécifié', ENT_QUOTES, 'UTF-8') ?>"
                                        data-demande-id="<?= htmlspecialchars($demande['id'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-reference="<?= htmlspecialchars($demande['reference_demande'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                                    <i class="fas fa-eye mr-1"></i> Détails
                                </button>
                                <a href="#" onclick="handleApproval(event, '/responsable/approuverDemande?id=<?= $demande['id'] ?>')" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <i class="fas fa-check mr-1"></i> Approuver
                                </a>
                                <a href="/responsable/rejeterDemande?id=<?= $demande['id'] ?>" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-times mr-1"></i> Rejeter
                                </a>
                            </div>
                        </div>
                        <div class="mt-5 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4">
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-500">Référence</p>
                                <p class="font-medium"><?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?></p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-500">Type</p>
                                <p class="font-medium"><?= htmlspecialchars($demande['type']) ?></p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-500">Période</p>
                                <p class="font-medium"><?= date('d/m/Y', strtotime($demande['date_debut'])) ?> - <?= date('d/m/Y', strtotime($demande['date_fin'])) ?></p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-500">Durée</p>
                                <p class="font-medium"><?= $demande['nbJours'] ?> jour(s)</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-sm text-gray-500">Motif</p>
                                <p class="font-medium"><?= htmlspecialchars($demande['motif'] ?? 'Non spécifié') ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal for details -->
    <div id="detailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Détails de la demande</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">Informations générales</h4>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <p class="text-sm text-gray-500">Référence</p>
                            <p id="modal-reference" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Employé</p>
                            <p id="modal-employee" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Type de congé</p>
                            <p id="modal-type" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Période</p>
                            <p id="modal-period" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Durée</p>
                            <p id="modal-duration" class="font-medium"></p>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">Motif</h4>
                    <p id="modal-motif" class="text-gray-700"></p>
                </div>
            </div>
            <div class="mt-6 flex justify-between space-x-3">
                <div>
                    <a id="modal-approve-link" href="#" onclick="handleModalApproval(event)" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-check mr-1"></i> Approuver
                    </a>
                    <a id="modal-reject-link" href="#" class="ml-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <i class="fas fa-times mr-1"></i> Rejeter
                    </a>
                </div>
                <button onclick="closeModal()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <script>
        // Event delegation for details buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to all details buttons
            document.querySelectorAll('.details-btn').forEach(function(button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get data from data attributes
                    const employee = this.dataset.employee || '';
                    const type = this.dataset.type || '';
                    const dateDebut = this.dataset.dateDebut || '';
                    const dateFin = this.dataset.dateFin || '';
                    const duree = this.dataset.duree || '';
                    const motif = this.dataset.motif || 'Non spécifié';
                    const demandeId = this.dataset.demandeId || '';
                    const reference = this.dataset.reference || '';

                    showDetails(employee, type, dateDebut, dateFin, duree, motif, demandeId, reference);
                });
            });
        });

        function showDetails(employee, type, dateDebut, dateFin, duree, motif, demandeId, reference) {
            console.log('showDetails called with:', {employee, type, dateDebut, dateFin, duree, motif, demandeId, reference});

            try {
                document.getElementById('modal-employee').textContent = employee;
                document.getElementById('modal-type').textContent = type;
                document.getElementById('modal-period').textContent = dateDebut + (dateDebut !== dateFin ? ' - ' + dateFin : '');
                document.getElementById('modal-duration').textContent = duree + ' jour' + (parseInt(duree) > 1 ? 's' : '');
                document.getElementById('modal-motif').textContent = motif || 'Aucun motif spécifié';

                // Update reference if element exists
                const referenceElement = document.getElementById('modal-reference');
                if (referenceElement) {
                    referenceElement.textContent = reference || '-';
                }

                // Set the links for approve and reject
                const approveUrl = '/responsable/approuverDemande?id=' + demandeId;
                const rejectUrl = '/responsable/rejeterDemande?id=' + demandeId;

                console.log('Setting modal links:', {approveUrl, rejectUrl});

                const approveLink = document.getElementById('modal-approve-link');
                const rejectLink = document.getElementById('modal-reject-link');

                if (approveLink) approveLink.href = approveUrl;
                if (rejectLink) rejectLink.href = rejectUrl;

                document.getElementById('detailsModal').classList.remove('hidden');
            } catch (error) {
                console.error('Error in showDetails:', error);
                alert('Erreur lors de l\'affichage des détails. Veuillez réessayer.');
            }
        }

        function closeModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }

        // Modern confirmation for approval actions
        async function handleApproval(event, url) {
            event.preventDefault();
            console.log('handleApproval called with URL:', url);

            try {
                // Check if confirmSuccess function exists
                if (typeof confirmSuccess !== 'function') {
                    console.error('confirmSuccess function not found, falling back to native confirm');
                    if (confirm('Êtes-vous sûr de vouloir approuver cette demande de congé ?')) {
                        window.location.href = url;
                    }
                    return;
                }

                const confirmed = await confirmSuccess(
                    'Êtes-vous sûr de vouloir approuver cette demande de congé ?',
                    'Approuver la demande'
                );

                console.log('Confirmation result:', confirmed);
                if (confirmed) {
                    console.log('Redirecting to:', url);
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in approval confirmation:', error);
                // Fallback to native confirm
                if (confirm('Êtes-vous sûr de vouloir approuver cette demande de congé ?')) {
                    window.location.href = url;
                }
            }
        }

        // Modern confirmation for modal approval
        async function handleModalApproval(event) {
            event.preventDefault();
            console.log('handleModalApproval called');

            try {
                // Check if confirmSuccess function exists
                if (typeof confirmSuccess !== 'function') {
                    console.error('confirmSuccess function not found, falling back to native confirm');
                    if (confirm('Êtes-vous sûr de vouloir approuver cette demande de congé ?')) {
                        const url = event.target.href;
                        window.location.href = url;
                    }
                    return;
                }

                const confirmed = await confirmSuccess(
                    'Êtes-vous sûr de vouloir approuver cette demande de congé ?',
                    'Approuver la demande'
                );

                console.log('Modal confirmation result:', confirmed);
                if (confirmed) {
                    const url = event.target.href;
                    console.log('Modal redirecting to:', url);
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in modal approval confirmation:', error);
                // Fallback to native confirm
                if (confirm('Êtes-vous sûr de vouloir approuver cette demande de congé ?')) {
                    const url = event.target.href;
                    window.location.href = url;
                }
            }
        }

        // Filter functionality for summary cards
        let currentFilter = 'all';

        function filterRequests(filterType) {
            console.log('Filtering requests by:', filterType);
            currentFilter = filterType;

            // Update card visual states
            updateCardStates(filterType);

            // Filter the request items
            const requestItems = document.querySelectorAll('.request-item');
            let visibleCount = 0;

            requestItems.forEach(item => {
                const itemType = item.getAttribute('data-type');
                const shouldShow = filterType === 'all' || itemType === filterType;

                if (shouldShow) {
                    item.style.display = 'block';
                    item.classList.remove('hidden');
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                    item.classList.add('hidden');
                }
            });

            // Show/hide empty state message
            updateEmptyState(visibleCount, filterType);

            console.log(`Filtered: ${visibleCount} items visible for filter "${filterType}"`);
        }

        function updateCardStates(activeFilter) {
            const cards = document.querySelectorAll('.filter-card');

            cards.forEach(card => {
                const cardFilter = card.getAttribute('data-filter');

                if (cardFilter === activeFilter) {
                    // Active state
                    card.classList.add('ring-2', 'ring-offset-2');
                    card.classList.add('shadow-lg', 'scale-105');
                    card.style.transform = 'scale(1.05)';
                } else {
                    // Inactive state
                    card.classList.remove('ring-2', 'ring-offset-2');
                    card.classList.remove('shadow-lg', 'scale-105');
                    card.style.transform = 'scale(1)';
                }
            });
        }

        function updateEmptyState(visibleCount, filterType) {
            const container = document.getElementById('requests-container');
            let emptyMessage = document.getElementById('filter-empty-message');

            if (visibleCount === 0) {
                if (!emptyMessage) {
                    emptyMessage = document.createElement('div');
                    emptyMessage.id = 'filter-empty-message';
                    emptyMessage.className = 'card text-center';
                    emptyMessage.innerHTML = `
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 mb-4">
                            <i class="fas fa-filter text-gray-400 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune demande trouvée</h3>
                        <p class="text-gray-500">Aucune demande ne correspond au filtre sélectionné.</p>
                        <button onclick="filterRequests('all')" class="mt-4 text-indigo-600 hover:text-indigo-800">
                            <i class="fas fa-times mr-1"></i> Effacer le filtre
                        </button>
                    `;
                    container.appendChild(emptyMessage);
                }
                emptyMessage.style.display = 'block';
            } else {
                if (emptyMessage) {
                    emptyMessage.style.display = 'none';
                }
            }
        }

        function handleCardKeydown(event, filterType) {
            // Handle Enter and Space key presses for accessibility
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                filterRequests(filterType);
            }
        }

        // Initialize filter state on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state
            updateCardStates('all');

            // Add keyboard navigation for cards
            const cards = document.querySelectorAll('.filter-card');
            cards.forEach((card, index) => {
                card.addEventListener('keydown', function(e) {
                    if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                        e.preventDefault();
                        const nextCard = cards[index + 1] || cards[0];
                        nextCard.focus();
                    } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                        e.preventDefault();
                        const prevCard = cards[index - 1] || cards[cards.length - 1];
                        prevCard.focus();
                    }
                });
            });
        });
    </script>

    <style>
        /* Enhanced styles for filter cards */
        .filter-card {
            transition: all 0.2s ease-in-out;
        }

        .filter-card:hover {
            transform: scale(1.02);
        }

        .filter-card:focus {
            outline: none;
        }

        .filter-card.active {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Smooth transitions for request items */
        .request-item {
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        .request-item.hidden {
            opacity: 0;
            transform: translateY(-10px);
        }

        /* Focus styles for better accessibility */
        .filter-card:focus-visible {
            outline: 2px solid #4F46E5;
            outline-offset: 2px;
        }
    </style>
</body>
</html>
